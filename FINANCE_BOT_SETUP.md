# Finance Bot Setup Guide

This guide will help you set up the Finance Bot for the Wiz Aroma Delivery system.

## Important: Chat Initialization

The most common error when setting up the Finance Bot is the "chat not found" error. This happens because the Finance Bot needs to have an active chat with the finance admin user.

## Steps to Set Up the Finance Bot

1. **Get your Telegram ID**
   - Your Telegram ID is set in the `.env` file as `FINANCE_CHAT_ID`
   - You can find your Telegram ID using bots like @userinfobot

2. **Start a chat with the Finance Bot**
   - Open Telegram and search for your Finance Bot by username
   - The Finance Bot username is: `@wiz_aroma_finance_bot` (or whatever you named it)
   - Start a chat with the bot by clicking "Start" or sending the `/start` command

3. **Verify the connection**
   - The Finance Bot should respond to your `/start` command
   - If it doesn't respond, make sure the bot is running

## Testing the Finance Bot

To test if the Finance Bot is properly set up:

1. Run the User Bot and the Finance Bot:
   ```bash
   python main.py --bot user
   python main.py --bot finance
   ```

   Or run all bots at once:
   ```bash
   python main.py --bot all
   ```

2. Place a test order through the User Bot

3. When prompted for payment, select any payment method and send a screenshot

4. Check if the Finance Bot receives the payment screenshot
   - If it does, the setup is successful
   - If it doesn't, check the logs for errors

## Troubleshooting

If you encounter the "chat not found" error:

1. Make sure the Finance Bot is running
2. Make sure you've started a chat with the Finance Bot from the account with the ID specified in your .env file
3. Check that the `FINANCE_CHAT_ID` in the `.env` file matches your Telegram ID
4. Restart both the User Bot and Finance Bot

## Common Errors

### "Chat not found" Error

This error occurs when the Finance Bot tries to send a message to a chat that doesn't exist. To fix this:

1. Make sure the user with the ID specified in your .env file has started a chat with the Finance Bot
2. If you want to use a different Telegram ID, update the `FINANCE_CHAT_ID` in the `.env` file

### "Conflict: terminated by other getUpdates request" Error

This error occurs when multiple instances of the same bot are running. To fix this:

1. Stop all running bots
2. Make sure each bot has a unique token in the `.env` file
3. Run the bots individually
