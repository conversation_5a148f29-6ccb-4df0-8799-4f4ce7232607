<div align="center">

# 🍽️ Wiz-Aroma Delivery Bot System

### *Intelligent Food Delivery Management Platform*

[![Python](https://img.shields.io/badge/Python-3.9+-blue.svg)](https://python.org)
[![Telegram Bot API](https://img.shields.io/badge/Telegram-Bot%20API-blue.svg)](https://core.telegram.org/bots/api)
[![Firebase](https://img.shields.io/badge/Firebase-Realtime%20DB-orange.svg)](https://firebase.google.com)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Version](https://img.shields.io/badge/Version-1.3.3-brightgreen.svg)](https://github.com/Mih-Nig-Afe/Wiz-Aroma-V-1.3.3)

*A comprehensive multi-bot Telegram-based food delivery system with advanced automation capabilities*

[🚀 Features](#-current-features) • [📋 Documentation](#-documentation) • [🔧 Installation](#-installation) • [🎯 Roadmap](#-development-roadmap)

</div>

---

## 📊 **System Status**

| Component | Status | Version | Performance |
|-----------|--------|---------|-------------|
| **User Bot** | ✅ Active | v1.3.3 | 99.2% Uptime |
| **Admin Bot** | ✅ Active | v1.3.3 | Stable |
| **Finance Bot** | ✅ Active | v1.3.3 | Manual Processing |
| **Maintenance Bot** | ✅ Active | v1.3.3 | Stable |
| **Notification Bot** | ✅ Active | v1.3.3 | Real-time |
| **Database** | ✅ Firebase | Realtime DB | 200-500ms Response |

## 🎯 **Project Overview**

Wiz-Aroma is an advanced multi-bot Telegram delivery system currently serving **30-80 orders daily** with plans for **full automation** and **3x capacity expansion**. The system features a sophisticated architecture with specialized bots for different operational functions.

## 🚀 **Current Features**

### 👥 **Customer Experience**

- 🏪 **Multi-Restaurant Selection**: Browse restaurants by geographical area
- 📱 **Smart Menu System**: Intuitive categorized interface with real-time pricing
- 💫 **Points Reward System**: Earn 11% of delivery fee as loyalty points
- 💳 **Multiple Payment Methods**: Telebirr, CBE Bank, BOA Bank, Points redemption
- ⭐ **Favorite Orders**: One-click reordering of preferred meals
- 📍 **Delivery Tracking**: Basic order status updates
- ⏰ **Operating Hours**: Weekdays (5:30-7:30, 11:30-14:30), Weekends (5:30-14:30)

### 🤖 **Multi-Bot Architecture**

- **🛍️ User Bot** (`@wiz_aroma_bot`): Customer ordering interface
- **👨‍💼 Admin Bot** (`@Wiz_aroma_admin_bot`): Order management and oversight
- **💰 Finance Bot** (`@Wiz_Aroma_Finance_bot`): Payment verification (currently manual)
- **🔧 Maintenance Bot** (`@Wiz_Aroma_Maintenance_bot`): System configuration
- **📢 Notification Bot**: Automated order notifications

### 🛠️ **Technical Features**

- **🔥 Firebase Integration**: Real-time database with local backup
- **🔒 Security**: Role-based access control and secure credential management
- **📊 Logging**: Comprehensive audit trails and error tracking
- **⚡ Performance**: 99.2% uptime with watchdog monitoring
- **🔄 Data Sync**: Automatic synchronization between cloud and local storage

## 🔮 **Planned Enhancements (V-2.0)**

### 🎯 **Major Upgrades in Development**

#### ⚡ **Automated Payment Verification**

- **Current**: Manual receipt verification (5-15 minutes)
- **Upgrade**: API-integrated transaction verification (<30 seconds)
- **Impact**: 95% faster processing, eliminate human errors

#### 🚚 **Intelligent Order Distribution**

- **Current**: Manual coordination via notification channels
- **Upgrade**: AI-powered automatic assignment to delivery personnel
- **Impact**: Instant assignment, capacity management (max 5 orders per person)

#### 📊 **Data Management Automation**

- **Current**: Manual tracking and calculation processes
- **Upgrade**: Real-time analytics dashboard with automated reporting
- **Impact**: Automated data processing, comprehensive system insights

#### 🚀 **Performance Optimization**

- **Current**: Single-threaded operations, basic monitoring
- **Upgrade**: Caching layer, async processing, advanced monitoring
- **Impact**: 3x throughput increase, 99.9% uptime target

## 📱 **Quick Start Guide**

### 🛍️ **For Customers**

1. **Start Ordering**: Send `/start` to `@wiz_aroma_bot`
2. **Main Menu Options**:
   - 🍽️ **Order Food** - Browse and order from restaurants
   - 💫 **My Points** - Check loyalty points balance
   - ⭐ **My Favorites** - Quick reorder saved meals
   - ℹ️ **Help** - Get assistance and support

3. **Ordering Process**:

   ```
   Select Area → Choose Restaurant → Add Items →
   Delivery Details → Payment → Verification → Delivery
   ```

### 👨‍💼 **For Administrators**

- **Order Management**: Review and approve/reject incoming orders
- **Customer Communication**: Direct messaging with customers
- **System Monitoring**: Track order flow and system performance

## 🔧 **Installation & Setup**

### Prerequisites

```bash
Python 3.9+
Firebase Account
Telegram Bot Tokens
```

### Quick Installation

```bash
# Clone the repository
git clone https://github.com/Mih-Nig-Afe/Wiz-Aroma-V-1.3.3.git
cd Wiz-Aroma-V-1.3.3

# Install dependencies
pip install -r requirements.txt

# Configure environment variables
cp .env.example .env
# Edit .env with your bot tokens and Firebase credentials

# Run the system
python main.py
```

## 📋 **Documentation**

### 📚 **Current Documentation**

| Document | Description | Status |
|----------|-------------|--------|
| **[CURRENT_SYSTEM_ANALYSIS.md](CURRENT_SYSTEM_ANALYSIS.md)** | Comprehensive system analysis | ✅ Complete |
| **[ENHANCEMENT_PROPOSAL.md](ENHANCEMENT_PROPOSAL.md)** | V-2.0 technical specifications | ✅ Complete |
| **[ACADEMIC_PROPOSAL.md](ACADEMIC_PROPOSAL.md)** | Academic project proposal | ✅ Complete |
| **[TODO_ROADMAP.md](TODO_ROADMAP.md)** | Development roadmap | ✅ Complete |
| **[PROJECT_SUMMARY.md](PROJECT_SUMMARY.md)** | Executive summary | ✅ Complete |

### 🛠️ **Technical Documentation**

- **[SYSTEM_ARCHITECTURE.md](SYSTEM_ARCHITECTURE.md)** - System architecture overview
- **[PROJECT_STRUCTURE.md](PROJECT_STRUCTURE.md)** - Project structure details
- **[FIREBASE_SETUP.md](FIREBASE_SETUP.md)** - Firebase integration guide
- **[DEPLOYMENT.md](DEPLOYMENT.md)** - Deployment instructions
- **[SECURITY.md](SECURITY.md)** - Security best practices

### 📊 **Configuration Files**

- **menu_and_restaurants.txt** - Restaurant and menu data
- **delivery_fees.txt** - Delivery locations and pricing
- **DATA_SYNC.md** - Data synchronization guide

## 🎯 **Development Roadmap**

### 🚧 **Phase 1: Foundation** (Weeks 1-4)

- [x] System analysis and documentation
- [ ] Development environment setup
- [ ] Performance optimization foundation

### ⚡ **Phase 2: Payment Automation** (Weeks 5-8)

- [ ] Payment gateway integration
- [ ] Automated verification system
- [ ] Transaction ID validation

### 🚚 **Phase 3: Order Distribution** (Weeks 9-12)

- [ ] Delivery management bot
- [ ] Intelligent assignment algorithms
- [ ] Real-time tracking system

### 📊 **Phase 4: Data Analytics** (Weeks 13-16)

- [ ] Automated data tracking
- [ ] Performance analytics
- [ ] System reporting dashboard

### 🧪 **Phase 5: Testing & Deployment** (Weeks 17-20)

- [ ] System integration testing
- [ ] Performance optimization
- [ ] Production deployment

## 📞 **Support & Contact**

### 🆘 **Get Help**

- **📧 Email**: [<EMAIL>](mailto:<EMAIL>)
- **💬 Telegram**: [@wiz_aroma_bot](https://t.me/wiz_aroma_bot)
- **🐛 Issues**: [GitHub Issues](https://github.com/Mih-Nig-Afe/Wiz-Aroma-V-1.3.3/issues)

### 👨‍💻 **Developer**

**Mihretab Nigatu**

- GitHub: [@Mih-Nig-Afe](https://github.com/Mih-Nig-Afe)
- Email: <<EMAIL>>

## 📈 **Project Statistics**

| Metric | Current | Target (V-2.0) |
|--------|---------|----------------|
| **Daily Orders** | 30-80 | 200+ |
| **Processing Time** | 15-25 min | <5 min |
| **System Uptime** | 99.2% | 99.9% |
| **Manual Tasks** | 70% | <10% |
| **Error Rate** | ~5% | <1% |

---

<div align="center">

### 🌟 **Star this repository if you find it helpful!**

**Made with ❤️ by [Mihretab Nigatu](https://github.com/Mih-Nig-Afe)**

*Transforming food delivery through intelligent automation*

</div>
