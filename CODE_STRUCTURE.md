# Code Structure

This document provides a detailed explanation of the code structure for the Wiz Aroma Delivery Bot system.

## Directory Structure

```
Wiz-Aroma-Adama/
├── main.py                  # Main entry point
├── check_and_run.py         # Script to check and run specific bots
├── start_user_bot.bat       # Batch file to start user bot
├── start_admin_bot.bat      # Batch file to start admin bot
├── start_finance_bot.bat    # Batch file to start finance bot
├── start_maintenance_bot.bat # Batch file to start maintenance bot
├── start_all_bots.bat       # Batch file to start all bots
├── requirements.txt         # Python dependencies
├── .env                     # Environment variables (not in repo)
├── .env.example             # Example environment variables
├── Procfile                 # For Railway deployment
├── data_files/              # Directory for data storage
│   ├── user_points.json     # User points data
│   ├── user_names.json      # User names data
│   ├── user_phone_numbers.json # User phone numbers data
│   ├── user_order_history.json # User order history data
│   ├── favorite_orders.json # User favorite orders data
│   ├── current_orders.json  # Current active orders
│   ├── order_status.json    # Status of orders in progress
│   ├── pending_admin_reviews.json # Orders waiting for admin review
│   ├── admin_remarks.json   # Admin remarks on orders
│   ├── awaiting_receipt.json # Orders waiting for payment receipt
│   ├── areas.json           # Available areas
│   ├── restaurants.json     # Available restaurants
│   ├── menus.json           # Restaurant menus
│   ├── delivery_locations.json # Available delivery locations
│   └── delivery_fees.json   # Delivery fees for different locations
└── src/                     # Source code
    ├── __init__.py          # Package initialization
    ├── config.py            # Configuration and environment variables
    ├── bot_instance.py      # Bot instance initialization
    ├── data_models.py       # Data models and structures
    ├── data_storage.py      # Data storage functions
    ├── data/                # Data initialization
    │   ├── __init__.py      # Package initialization
    │   └── menus.py         # Menu initialization
    ├── handlers/            # Bot handlers
    │   ├── __init__.py      # Handler initialization
    │   ├── main_handlers.py # Main menu handlers
    │   ├── order_handlers.py # Order flow handlers
    │   ├── admin_handlers.py # Admin bot handlers
    │   ├── payment_handlers.py # Payment processing handlers
    │   ├── user_profile_handlers.py # User profile handlers
    │   ├── favorite_orders_handlers.py # Favorite orders handlers
    │   ├── maintenance_handlers.py # Maintenance bot handlers
    │   └── location_handlers.py # Location management handlers
    └── utils/               # Utility functions
        ├── __init__.py      # Package initialization
        ├── error_handling.py # Error handling utilities
        ├── handler_registration.py # Handler registration utilities
        ├── keyboards.py     # Keyboard markup utilities
        ├── logging_utils.py # Logging utilities
        ├── text_utils.py    # Text formatting utilities
        ├── time_utils.py    # Time-related utilities
        ├── validation.py    # Input validation utilities
        └── data_consistency.py # Data consistency utilities
```

## Core Components

### Entry Points

- **main.py**: The main entry point for the application. It initializes the bots, sets up error handling, and starts the bot polling.
- **check_and_run.py**: A script to check if a bot is already running and start it if not.

### Configuration

- **config.py**: Contains all configuration variables, including environment variables loaded from `.env`.
- **.env**: Contains sensitive information like bot tokens and chat IDs (not included in the repository).

### Bot Instances

- **bot_instance.py**: Initializes the bot instances to avoid circular imports. Creates the user, admin, finance, and maintenance bot instances.

### Data Models

- **data_models.py**: Defines the data structures used throughout the application, including in-memory dictionaries for user data, orders, and configuration.

### Data Storage

- **data_storage.py**: Contains functions for loading and saving data to/from JSON files. Handles data persistence and backup.

## Handlers

The handlers directory contains all the bot handlers, organized by functionality:

### Main Handlers

- **main_handlers.py**: Handles the main menu and general commands like `/start` and `/help`.

### Order Handlers

- **order_handlers.py**: Handles the order flow, from selecting an area to confirming an order.

### Admin Handlers

- **admin_handlers.py**: Handles admin actions like reviewing orders, adding remarks, and approving/rejecting orders.

### Payment Handlers

- **payment_handlers.py**: Handles payment processing, including selecting payment methods and verifying payments.

### User Profile Handlers

- **user_profile_handlers.py**: Handles user profile management, including viewing and updating user information.

### Favorite Orders Handlers

- **favorite_orders_handlers.py**: Handles saving, viewing, and reordering favorite orders.

### Maintenance Handlers

- **maintenance_handlers.py**: Handles maintenance actions like managing areas, restaurants, and menus.

### Location Handlers

- **location_handlers.py**: Handles location management, including adding, updating, and deleting delivery locations.

## Utilities

The utils directory contains utility functions used throughout the application:

### Error Handling

- **error_handling.py**: Contains utilities for handling exceptions and errors.

### Handler Registration

- **handler_registration.py**: Contains utilities for registering handlers with the bot instances.

### Keyboards

- **keyboards.py**: Contains functions for creating keyboard markups for user interaction.

### Logging

- **logging_utils.py**: Contains utilities for logging messages and errors.

### Text Utilities

- **text_utils.py**: Contains utilities for formatting text, including Markdown escaping.

### Time Utilities

- **time_utils.py**: Contains utilities for time-related operations, including checking if the service is open.

### Validation

- **validation.py**: Contains utilities for validating user input, including phone numbers.

### Data Consistency

- **data_consistency.py**: Contains utilities for ensuring data consistency across the application.

## Handler Registration System

The system uses a centralized handler registration system to avoid circular imports and ensure all handlers are properly registered:

1. Handlers are defined in their respective modules with the `@register_handler` decorator.
2. The decorator registers the handler with the `HandlerRegistry` singleton.
3. When the application starts, `register_all_handlers()` is called to register all handlers with their respective bot instances.

Example:

```python
@register_handler("user", func=lambda message: message.text == "🍽️ Order Food")
def handle_order_food(message):
    # Handler implementation
    pass
```

## Data Flow

1. **User Interaction**: Users interact with the User Bot, which calls the appropriate handlers.
2. **Data Processing**: Handlers process user input and update in-memory data structures.
3. **Data Persistence**: Data is periodically saved to JSON files for persistence.
4. **Bot Communication**: Bots communicate with each other through shared data structures and by sending messages to users.

## Error Handling

The system includes comprehensive error handling:

1. **Exception Handling**: All operations are wrapped in try-except blocks.
2. **Logging**: Detailed logging of all operations and errors.
3. **Data Consistency**: Regular checks to ensure data consistency.
4. **Backup**: Automatic backup of data files before writing.

## Deployment

The system is designed to be deployed on Railway:

1. **Procfile**: Defines the command to run the application.
2. **Environment Variables**: All sensitive information is stored in environment variables.
3. **Requirements**: All dependencies are listed in `requirements.txt`.
