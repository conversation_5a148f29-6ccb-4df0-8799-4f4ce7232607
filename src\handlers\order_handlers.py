"""
Order handlers for the Wiz Aroma Delivery Bot.
Contains handlers for the order flow.
"""

from telebot import types

from src.bot_instance import bot
from src.utils.keyboards import (
    get_main_menu_markup,
    get_restaurants_markup,
    get_menu_items_markup,
    get_delivery_gates_markup,
    get_delivery_name_markup,
    get_phone_number_markup,
    get_order_confirmation_markup,
    get_areas_markup,
)
from src.data_models import (
    orders,
    order_status,
    user_names,
    user_phone_numbers,
    current_order_numbers,
)
from src.data_storage import (
    save_user_data,
    clean_up_order_data,
    get_all_areas,
    get_all_restaurants,
    get_restaurant_by_id,
    get_restaurant_menu,
    get_area_by_id,
    get_restaurants_by_area,
    update_points_balance,
    get_area_id_by_name,
    set_current_order_area,
    get_all_delivery_locations,
    get_delivery_fee,
    get_delivery_location_by_id,
)
from src.utils.time_utils import is_open_now, generate_order_number
from src.utils.validation import is_valid_phone
from src.utils.text_utils import escape_markdown
from src.handlers.admin_handlers import send_order_for_review
from src.config import logger
from src.firebase_db import get_user_names, get_user_phone_numbers


@bot.message_handler(
    func=lambda message: message.text and message.text == "🍽️ Order Food"
)
def handle_area_selection(message):
    """Show areas for food delivery"""
    try:
        # Check if the bot is currently open for orders
        if not is_open_now():
            markup = get_main_menu_markup()

            bot.send_message(
                message.chat.id,
                "⏰ Sorry, we are currently closed for orders!\n\n"
                "Our working hours are:\n"
                "• Mon-Fri: 5:30-7:30 & 11:30-2:30\n"
                "• Sat-Sun: 5:30-2:30\n"
                "(Ethiopian Local Time)\n\n"
                "Please come back during our operating hours. Thank you!",
                reply_markup=markup,
            )
            return

        # Get markup with areas from data storage
        markup = get_areas_markup()

        bot.send_message(
            message.chat.id,
            "📍 Please select your area for delivery:",
            reply_markup=markup,
        )
    except Exception as e:
        logger.error(f"Error in handle_area_selection: {e}")
        bot.send_message(message.chat.id, "❌ Error loading areas. Please try again.")


@bot.message_handler(
    func=lambda message: message.text and message.text.startswith("📍 ")
)
def handle_area_restaurants(message):
    """Handle restaurants for selected area"""
    try:
        user_id = message.from_user.id
        area_name = message.text.replace("📍 ", "")

        logger.info(f"User {user_id} selected area: {area_name}")

        # Try to find area in dynamic data first
        areas = get_all_areas()
        area_id = None

        # Find area id by name in dynamic data
        for area in areas:
            if area["name"] == area_name:
                area_id = area["id"]
                break

        # If area found in dynamic data, get restaurants from data storage
        if area_id:
            # Get restaurants for this area from data storage
            restaurants_list = get_restaurants_by_area(area_id)

            if restaurants_list:
                logger.info(
                    f"Found {len(restaurants_list)} restaurants in area {area_name} from data storage"
                )
                # Show restaurants markup using the restaurants list
                bot.send_message(
                    user_id,
                    f"Please select a restaurant in {area_name}:",
                    reply_markup=get_restaurants_markup(restaurants_list),
                )
                # Store area information in user's current order
                set_current_order_area(user_id, area_name, area_id)
                return
            else:
                logger.warning(
                    f"No restaurants found in data storage for area {area_name} (id: {area_id})"
                )

        # Fallback to static config if needed
        from src.config import restaurants as config_restaurants

        if area_name in config_restaurants:
            logger.info(f"Using static config for area {area_name}")
            # Show restaurants markup using area name
            bot.send_message(
                user_id,
                f"Please select a restaurant in {area_name}:",
                reply_markup=get_restaurants_markup(area_name),
            )
            # Store area information in user's current order
            # For static config, we need to map area_name to an ID
            set_current_order_area(
                user_id, area_name, area_id or get_area_id_by_name(area_name)
            )
        else:
            logger.warning(f"Area {area_name} not found in static config")
            bot.send_message(
                user_id,
                "I couldn't find any restaurants in that area. Please try another area.",
                reply_markup=get_areas_markup(),
            )
    except Exception as e:
        logger.error(f"Error in handle_area_restaurants: {e}")
        bot.send_message(
            message.from_user.id,
            "Sorry, I encountered an error showing restaurants. Please try again.",
            reply_markup=get_areas_markup(),
        )


@bot.message_handler(
    func=lambda message: message.text and message.text.startswith("🏪 ")
)
def handle_restaurant_selection(message):
    """Handle restaurant selection"""
    try:
        # Check if the bot is currently open for orders
        if not is_open_now():
            markup = get_main_menu_markup()

            bot.send_message(
                message.chat.id,
                "⏰ Sorry, we are currently closed for orders!\n\n"
                "Our working hours are:\n"
                "• Mon-Fri: 5:30-7:30 & 11:30-2:30\n"
                "• Sat-Sun: 5:30-2:30\n"
                "(Ethiopian Local Time)\n\n"
                "Please come back during our operating hours. Thank you!",
                reply_markup=markup,
            )
            return

        user_id = message.from_user.id
        restaurant_name = message.text.replace("🏪 ", "")
        logger.info(f"User {user_id} is selecting restaurant: {restaurant_name}")

        # Get fresh restaurants data
        all_restaurants = get_all_restaurants()
        logger.debug(f"Found {len(all_restaurants)} restaurants in database")

        # Find restaurant by name
        restaurant_id = None
        restaurant_area_id = None

        for restaurant in all_restaurants:
            if restaurant["name"] == restaurant_name:
                restaurant_id = restaurant["id"]
                restaurant_area_id = restaurant["area_id"]
                logger.info(
                    f"Found restaurant ID {restaurant_id} in area {restaurant_area_id}"
                )
                break

        if not restaurant_id:
            logger.warning(f"Restaurant not found: {restaurant_name}")
            bot.send_message(message.chat.id, "❌ Restaurant not found.")
            return

        # Get area info for the restaurant
        area = get_area_by_id(restaurant_area_id)
        area_name = area["name"] if area else "Unknown Area"
        logger.info(f"Restaurant {restaurant_name} is in area: {area_name}")

        # Initialize or reset user's order
        orders[user_id] = {
            "restaurant": restaurant_name,
            "restaurant_id": restaurant_id,
            "restaurant_area": area_name,
            "items": [],
        }
        logger.debug(f"Initialized order data for user {user_id}")

        # Show restaurant menu - load directly from storage
        try:
            # Ensure restaurant_id is properly treated as an integer when needed
            logger.debug(f"Attempting to load menu for restaurant ID {restaurant_id}")
            menu = get_restaurant_menu(restaurant_id)

            if menu:
                logger.info(
                    f"Loaded menu with {len(menu)} items for restaurant {restaurant_id}"
                )
                markup = get_menu_items_markup(restaurant_id, menu)

                menu_text = (
                    f"📋 Menu for {restaurant_name}\n\n"
                    "Please select items from the keyboard menu below (max 5 items per order).\n\n"
                    "When you're done, click '✅ Complete Order'."
                )

                bot.send_message(message.chat.id, menu_text, reply_markup=markup)
                order_status[user_id] = "SELECTING_ITEMS"
            else:
                logger.warning(f"No menu items found for restaurant {restaurant_id}")
                bot.send_message(
                    message.chat.id,
                    "❌ Menu not available for this restaurant. Please try another restaurant or contact support.",
                )
        except Exception as menu_error:
            logger.error(
                f"Error loading menu for restaurant {restaurant_id}: {menu_error}",
                exc_info=True,
            )
            bot.send_message(
                message.chat.id,
                "❌ There was an error loading the menu. Please try another restaurant or contact support.",
            )

    except Exception as e:
        logger.error(f"Error in handle_restaurant_selection: {e}", exc_info=True)
        bot.send_message(
            message.chat.id, "❌ Error selecting restaurant. Please try again."
        )


@bot.message_handler(
    func=lambda message: message.text and message.text.startswith("➕ ")
)
def add_menu_item(message):
    """Add a menu item to the order"""
    try:
        user_id = message.from_user.id
        if user_id in orders:
            # Extract item name and price from message
            menu_text = message.text.replace("➕ ", "")

            # Format could be either "Item Name (Price birr)" or "Item Name (Size) (Price birr)"
            if menu_text.count("(") > 1:
                # Handle format with size: "Item Name (Size) (Price birr)"
                item_name = menu_text.rsplit(" (", 1)[0].strip()
                # Extract just the price part from the last parenthesis
                price_part = menu_text.rsplit(" (", 1)[1]
                item_price = int(price_part.split(" birr)")[0].strip())
            else:
                # Handle standard format: "Item Name (Price birr)"
                item_name = menu_text.split(" (")[0].strip()
                item_price = int(menu_text.split("(")[1].split(" birr)")[0].strip())

            # Get restaurant info and menu
            restaurant_id = orders[user_id]["restaurant_id"]
            restaurant_menu = get_restaurant_menu(restaurant_id)

            # Check if we can add another item (max 5)
            if len(orders[user_id]["items"]) >= 5:
                bot.send_message(
                    message.chat.id,
                    "❌ You've reached the maximum of 5 items per order. Please complete your order or cancel to start a new one.",
                )
                return

            # Find the item in the menu
            found_item = None
            for menu_item in restaurant_menu:
                if menu_item["name"] == item_name and menu_item["price"] == item_price:
                    found_item = menu_item
                    break

            if found_item:
                # Add the item to the order
                orders[user_id]["items"].append(
                    {
                        "id": found_item["id"],
                        "name": found_item["name"],
                        "price": found_item["price"],
                    }
                )

                # Update total price
                if "total_price" not in orders[user_id]:
                    orders[user_id]["total_price"] = 0
                orders[user_id]["total_price"] += found_item["price"]

                # Show order summary after adding item
                items_count = len(orders[user_id]["items"])
                remaining_items = 5 - items_count
                bot.send_message(
                    message.chat.id,
                    f"✅ Added {item_name} to your order!\n"
                    f"🛍️ Cart: {items_count} item(s)\n"
                    f"📝 You can add {remaining_items} more item(s)\n"
                    f"💰 Total: {orders[user_id]['total_price']} birr",
                )
            else:
                bot.send_message(message.chat.id, "❌ Item not found in the menu.")
        else:
            bot.send_message(message.chat.id, "❌ Please select a restaurant first.")
    except Exception as e:
        logger.error(f"Error in add_menu_item: {e}")
        bot.send_message(
            message.chat.id, "❌ Error adding item to order. Please try again."
        )


@bot.message_handler(func=lambda message: message.text == "✅ Complete Order")
def handle_order_confirmation(message):
    """Handle order completion"""
    try:
        user_id = message.from_user.id
        if user_id not in orders:
            bot.send_message(message.chat.id, "No active order found.")
            return

        # Check if there are items in the cart
        if not orders[user_id].get("items", []):
            bot.send_message(
                message.chat.id,
                "❌ Your cart is empty. Please add some items before completing your order.",
            )
            return

        # Check if this is a menu update (user already has order details)
        if (
            "order_description" in orders[user_id]
            and "delivery_gate" in orders[user_id]
            and "delivery_name" in orders[user_id]
            and "phone_number" in orders[user_id]
        ):

            # Generate order number if not already present
            if "order_number" not in orders[user_id]:
                order_number = generate_order_number(user_id)
                current_order_numbers[user_id] = order_number
                orders[user_id]["order_number"] = order_number

            # Skip to order summary
            generate_and_send_order_summary(message.chat.id, user_id)
            return

        # First ask for order description
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("⏩ Skip Description"),
            types.KeyboardButton("❌ Cancel Order"),
        )

        bot.send_message(
            message.chat.id,
            "📝 Please enter any special instructions or notes for your order:\n\n"
            "(e.g., 'Alicha 🟡', 'with berbere 🔴', 'Additional Extras')",
            reply_markup=markup,
        )
        order_status[user_id] = "AWAITING_ORDER_DESCRIPTION"

    except Exception as e:
        logger.error(f"Error in handle_order_confirmation: {str(e)}")
        bot.send_message(message.chat.id, "Error completing order. Please try again.")


@bot.message_handler(
    func=lambda message: order_status.get(message.from_user.id)
    == "AWAITING_ORDER_DESCRIPTION"
)
def handle_order_description(message):
    """Handle order description"""
    try:
        user_id = message.from_user.id

        if message.text == "❌ Cancel Order":
            clean_up_order_data(user_id, None)
            # Create markup with Order and Main Menu buttons
            markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
            markup.add(
                types.KeyboardButton("🍽️ Order Food"),
                types.KeyboardButton("🔙 Back to Main Menu"),
                types.KeyboardButton("💫 My Points"),
                types.KeyboardButton("ℹ️ Help"),
            )
            bot.reply_to(
                message,
                "Order cancelled. You can start a new order anytime!",
                reply_markup=markup,
            )
            return

        # Check if user wants to skip description
        if message.text == "⏩ Skip Description":
            description = "No special instructions"
        else:
            description = message.text.strip()

        # Save the description in the order
        orders[user_id]["order_description"] = description

        # Now ask for delivery location
        # Get the restaurant area ID
        try:
            restaurant_id = int(orders[user_id]["restaurant_id"])
            logger.info(f"Restaurant ID: {restaurant_id}, Type: {type(restaurant_id)}")

            # Update stored ID to ensure it's an integer
            orders[user_id]["restaurant_id"] = restaurant_id
        except (ValueError, TypeError) as e:
            logger.error(f"Failed to convert restaurant_id to integer: {e}")
            restaurant_id = orders[user_id]["restaurant_id"]
            logger.info(
                f"Using original restaurant_id: {restaurant_id}, Type: {type(restaurant_id)}"
            )

        restaurant = get_restaurant_by_id(restaurant_id)

        if restaurant is None:
            logger.error(f"Restaurant not found with ID: {restaurant_id}")
            bot.send_message(
                message.chat.id,
                "❌ Error: Restaurant not found. Please try again.",
            )
            return

        logger.info(f"Restaurant found: {restaurant}")

        try:
            restaurant_area_id = int(restaurant["area_id"])
            logger.info(
                f"Restaurant area ID: {restaurant_area_id}, Type: {type(restaurant_area_id)}"
            )
        except (ValueError, TypeError) as e:
            logger.error(f"Failed to convert restaurant_area_id to integer: {e}")
            restaurant_area_id = restaurant["area_id"]
            logger.info(
                f"Using original restaurant_area_id: {restaurant_area_id}, Type: {type(restaurant_area_id)}"
            )

        # Get the area name
        area = get_area_by_id(restaurant_area_id)
        logger.info(f"Area found: {area}")
        area_name = area["name"] if area else "Unknown Area"

        # Store the area ID in the order for later use
        orders[user_id]["restaurant_area_id"] = restaurant_area_id
        logger.info(
            f"Stored restaurant_area_id in order: {orders[user_id]['restaurant_area_id']}"
        )

        # Get delivery gates markup using area name and area ID
        markup = get_delivery_gates_markup(area_name, restaurant_area_id)

        bot.send_message(
            message.chat.id,
            "Please select your delivery location:",
            reply_markup=markup,
        )
        order_status[user_id] = "AWAITING_DELIVERY_LOCATION"

    except Exception as e:
        logger.error(f"Error in handle_order_description: {e}")
        bot.send_message(
            message.chat.id,
            "❌ Error processing your order description. Please try again.",
        )


@bot.message_handler(
    func=lambda message: message.text and message.text.startswith("🚪 ")
)
def handle_delivery_gate(message):
    """Handle delivery gate selection"""
    try:
        user_id = message.from_user.id

        logger.info(f"User {user_id} selected delivery gate: {message.text}")

        # Extract gate name and fee
        gate_text = message.text.replace("🚪 ", "")
        location_name = gate_text.split(" (")[0] if " (" in gate_text else gate_text

        # Extract the fee if present
        fee = 0
        if "(" in gate_text and "birr)" in gate_text:
            try:
                fee_text = gate_text.split("(")[1].split(" birr)")[0]
                fee = float(fee_text)
            except (ValueError, IndexError) as e:
                logger.error(f"Error parsing fee from '{gate_text}': {e}")
                fee = 0

        # Extract location ID from user's current order if available
        location_id = orders.get(user_id, {}).get("restaurant_area_id")
        if not location_id:
            # Try to get from delivery_locations_data by matching name
            for location in get_all_delivery_locations():
                if location["name"] == location_name:
                    location_id = location["id"]
                    break

        if not location_id:
            # Fallback to an arbitrary ID
            location_id = 1
            logger.warning(f"Using fallback location_id for {location_name}")

        # Store delivery gate info
        orders[user_id]["delivery_gate"] = location_name
        orders[user_id]["delivery_location_id"] = location_id
        orders[user_id]["delivery_fee"] = fee

        logger.info(
            f"Successfully stored delivery info: gate={location_name}, id={location_id}, fee={fee}"
        )

        # Make sure we load the latest user data
        names_data = get_user_names()
        if str(user_id) in names_data:
            user_names[user_id] = names_data[str(user_id)]

        # Now ask for delivery name
        saved_name = user_names.get(user_id)
        logger.info(f"Using saved name for user {user_id}: {saved_name}")
        markup = get_delivery_name_markup(saved_name)

        bot.send_message(
            message.chat.id, "Please enter Your name:", reply_markup=markup
        )
        order_status[user_id] = "AWAITING_DELIVERY_NAME"

    except Exception as e:
        logger.error(f"Error in handle_delivery_gate: {str(e)}")
        bot.send_message(
            message.chat.id, "❌ Error processing delivery location. Please try again."
        )


@bot.message_handler(
    func=lambda message: order_status.get(message.from_user.id)
    == "AWAITING_DELIVERY_NAME"
)
def handle_delivery_name(message):
    """Handle delivery name input"""
    user_id = message.from_user.id

    if message.text == "❌ Cancel Order":
        clean_up_order_data(user_id, None)
        # Create markup with Order and Main Menu buttons
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("🍽️ Order Food"),
            types.KeyboardButton("🔙 Back to Main Menu"),
            types.KeyboardButton("💫 My Points"),
            types.KeyboardButton("ℹ️ Help"),
        )
        bot.reply_to(
            message,
            "Order cancelled. You can start a new order anytime!",
            reply_markup=markup,
        )
        return

    # Handle saved name selection
    if message.text.startswith("📝 Use saved name:"):
        # Extract the name from the button text if needed, otherwise use stored value
        if user_id in user_names:
            delivery_name = user_names[user_id]
        else:
            # Extract name from button text as fallback
            delivery_name = message.text.replace("📝 Use saved name:", "").strip()
            user_names[user_id] = delivery_name
    else:
        delivery_name = message.text.strip()
        user_names[user_id] = delivery_name  # Save for future use

    # Save user data to ensure persistence
    save_user_data()

    orders[user_id]["delivery_name"] = delivery_name

    # Make sure we load the latest user phone number from Firebase
    from src.firebase_db import get_user_phone_numbers

    # Try to get user's saved phone from Firebase first
    phone_numbers_data = get_user_phone_numbers()
    if str(user_id) in phone_numbers_data:
        user_phone_numbers[user_id] = phone_numbers_data[str(user_id)]

    # Now ask for phone number
    saved_phone = user_phone_numbers.get(user_id)
    logger.info(f"Using saved phone for user {user_id}: {saved_phone}")
    markup = get_phone_number_markup(saved_phone)

    bot.send_message(
        message.chat.id, "Please enter your phone number:", reply_markup=markup
    )
    order_status[user_id] = "AWAITING_PHONE_NUMBER"


@bot.message_handler(
    func=lambda message: order_status.get(message.from_user.id)
    == "AWAITING_PHONE_NUMBER"
)
def handle_phone_number(message):
    """Handle phone number input"""
    user_id = message.from_user.id

    try:
        # Handle cancel request
        if message.text == "❌ Cancel Order":
            clean_up_order_data(user_id, None)
            # Create markup with Order and Main Menu buttons
            markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
            markup.add(
                types.KeyboardButton("🍽️ Order Food"),
                types.KeyboardButton("🔙 Back to Main Menu"),
                types.KeyboardButton("💫 My Points"),
                types.KeyboardButton("ℹ️ Help"),
            )
            bot.reply_to(
                message,
                "Order cancelled. You can start a new order anytime!",
                reply_markup=markup,
            )
            return

        # Get phone number from message
        phone_number = message.text.strip()

        # Check if the user selected "Use saved number"
        if phone_number.startswith("📱 Use saved number:"):
            # Extract the number from the button text if needed, otherwise use stored value
            if user_id in user_phone_numbers:
                phone_number = user_phone_numbers[user_id]
            else:
                # Extract number from button text as fallback
                phone_number = phone_number.replace("📱 Use saved number:", "").strip()
                user_phone_numbers[user_id] = phone_number

        # Validate phone number
        if not is_valid_phone(phone_number):
            bot.send_message(
                message.chat.id,
                "❌ Invalid phone number format. Please enter a valid phone number.",
            )
            return

        # Save the phone number in the order and persistently
        user_phone_numbers[user_id] = phone_number
        orders[user_id]["phone_number"] = phone_number

        # Save user data to ensure persistence
        save_user_data()

        # Generate order number directly instead of asking for email
        order_number = generate_order_number(user_id)
        current_order_numbers[user_id] = order_number
        orders[user_id]["order_number"] = order_number  # Store in order data

        # Store Telegram username in the order
        telegram_username = message.from_user.username or "Not provided"
        orders[user_id]["telegram_username"] = telegram_username

        # Generate and send order summary
        generate_and_send_order_summary(message.chat.id, user_id)

    except Exception as e:
        logger.error(f"Error in handle_phone_number: {e}")
        bot.send_message(
            message.chat.id, "❌ Error processing your phone number. Please try again."
        )


@bot.message_handler(
    func=lambda message: order_status.get(message.from_user.id)
    == "AWAITING_CONFIRMATION"
)
def handle_final_order_confirmation(message):
    """Handle final order confirmation"""
    try:
        user_id = message.from_user.id

        if message.text == "✅ Confirm Order":
            order = orders[user_id]
            # phone_number not used here
            order_number = order.get("order_number")

            # Add Telegram username to order data
            username = message.from_user.username
            order["username"] = username or "No username"

            # Regular order - send for admin review
            send_order_for_review(user_id, order)

            # Create markup with main menu option
            markup = types.ReplyKeyboardMarkup(row_width=1, resize_keyboard=True)
            markup.add(types.KeyboardButton("🔙 Back to Main Menu"))

            bot.send_message(
                message.chat.id,
                f"✅ Your order #{order_number} has been submitted for review!\n\n"
                "Please wait for admin approval before proceeding to payment.\n"
                "You will be notified once your order is approved.",
                reply_markup=markup,
            )

            # Update order status
            order_status[user_id] = "AWAITING_ADMIN_APPROVAL"

        elif message.text == "💾 Save as Favorite":
            # Import the prompt_for_favorite_name handler from favorite_orders_handlers
            from src.handlers.favorite_orders_handlers import prompt_for_favorite_name

            # Call the handler
            prompt_for_favorite_name(message)

        elif message.text == "🔄 Update Menu":
            # Handle update menu request
            handle_update_menu(message)

        elif message.text == "❌ Cancel Order":
            # Cancel order handling
            cancel_order(message)

        else:
            bot.send_message(
                message.chat.id,
                "Please confirm your order by selecting ✅ Confirm Order, 💾 Save as Favorite, 🔄 Update Menu, or ❌ Cancel Order.",
            )

    except Exception as e:
        logger.error(f"Error in handle_final_order_confirmation: {e}")
        bot.send_message(
            message.chat.id, "❌ Error processing your confirmation. Please try again."
        )


@bot.message_handler(func=lambda message: message.text == "❌ Cancel Order")
def cancel_order(message):
    """Handle order cancellation"""
    try:
        user_id = message.from_user.id

        # Check if user has an existing order
        if user_id not in orders:
            markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
            markup.add(
                types.KeyboardButton("🍽️ Order Food"),
                types.KeyboardButton("🔙 Back to Main Menu"),
            )
            bot.send_message(
                message.chat.id,
                "You don't have an active order to cancel.",
                reply_markup=markup,
            )
            return

        # Get order points info for restoring if needed
        points_used = orders[user_id].get("points_used", 0)

        # Restore points to user if they used points for this order
        if points_used > 0:
            # Import update_points_balance to restore points
            update_points_balance(user_id, points_used)
            logger.info(
                f"Restored {points_used} points to user {user_id} due to order cancellation"
            )

        # Get order number for log
        order_number = orders[user_id].get("order_number", "unknown")

        # Clean up all order data
        clean_up_order_data(user_id, order_number)

        # Return to main menu with Back to Main Menu button
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("🍽️ Order Food"),
            types.KeyboardButton("🔙 Back to Main Menu"),
            types.KeyboardButton("💫 My Points"),
            types.KeyboardButton("ℹ️ Help"),
        )

        bot.send_message(
            message.chat.id,
            "🚫 Order canceled. Your cart is now empty.",
            reply_markup=markup,
        )

        # Send additional confirmation that they can start over
        bot.send_message(
            message.chat.id,
            "You can start a new order whenever you're ready!",
        )
    except Exception as e:
        logger.error(f"Error in cancel_order: {e}")
        bot.send_message(message.chat.id, "❌ Error canceling order. Please try again.")


@bot.message_handler(func=lambda message: message.text == "🔙 Back to Areas")
def back_to_areas(message):
    """Return to the areas selection menu"""
    handle_area_selection(message)


@bot.message_handler(func=lambda message: message.text == "🔄 Update Menu")
def handle_update_menu(message):
    """Handle updating menu items while preserving user data"""
    try:
        user_id = message.from_user.id

        # Check if user has an existing order
        if user_id not in orders:
            bot.send_message(message.chat.id, "No active order found.")
            return

        # Get the restaurant ID from the current order
        restaurant_id = orders[user_id].get("restaurant_id")
        restaurant_name = orders[user_id].get("restaurant")

        if not restaurant_id:
            bot.send_message(
                message.chat.id, "❌ Error: Restaurant information not found."
            )
            return

        # Clear only the items in the order, keep other data
        orders[user_id]["items"] = []
        orders[user_id]["total_price"] = 0

        # Show restaurant menu again
        try:
            menu = get_restaurant_menu(restaurant_id)

            if menu:
                markup = get_menu_items_markup(restaurant_id, menu)

                menu_text = (
                    f"📋 Menu for {restaurant_name}\n\n"
                    "Please select items from the keyboard menu below (max 5 items per order).\n\n"
                    "When you're done, click '✅ Complete Order'."
                )

                bot.send_message(message.chat.id, menu_text, reply_markup=markup)
                order_status[user_id] = "SELECTING_ITEMS"
            else:
                bot.send_message(
                    message.chat.id,
                    "❌ Menu not available for this restaurant. Please try another restaurant or contact support.",
                )
        except Exception as menu_error:
            logger.error(
                f"Error loading menu for restaurant {restaurant_id}: {menu_error}"
            )
            bot.send_message(
                message.chat.id,
                "❌ There was an error loading the menu. Please try again or contact support.",
            )
    except Exception as e:
        logger.error(f"Error in handle_update_menu: {e}")
        bot.send_message(message.chat.id, "❌ Error updating menu. Please try again.")


def generate_and_send_order_summary(chat_id, user_id):
    """Generate and send order summary to the user"""
    try:
        # Get the order data
        order = orders[user_id]
        order_number = order.get("order_number")

        # Format order items
        items = order.get("items", [])

        # Count identical items
        item_counts = {}
        for item in items:
            name = item["name"]
            price = item["price"]

            if name in item_counts:
                item_counts[name]["count"] += 1
                item_counts[name]["total"] += price
            else:
                item_counts[name] = {"count": 1, "price": price, "total": price}

        # Format the order details
        order_details = ""
        for name, details in item_counts.items():
            # Escape Markdown in item name
            safe_name = escape_markdown(name)
            if details["count"] > 1:
                order_details += (
                    f"• {safe_name} x{details['count']}: {details['total']} birr\n"
                )
            else:
                order_details += f"• {safe_name}: {details['price']} birr\n"

        # Calculate total
        subtotal = sum(item["price"] for item in items)
        delivery_fee = order.get("delivery_fee", 0)
        total_price = subtotal + delivery_fee

        # Get delivery location
        delivery_gate = order.get("delivery_gate", "Unknown")
        # Escape Markdown in delivery gate
        safe_delivery_gate = escape_markdown(delivery_gate)

        # Get restaurant name
        restaurant_name = order.get("restaurant", "Unknown")
        # Escape Markdown in restaurant name
        safe_restaurant_name = escape_markdown(restaurant_name)

        # Get area name
        restaurant_area_id = order.get("restaurant_area_id")
        area = get_area_by_id(restaurant_area_id)
        area_name = area["name"] if area else "Unknown Area"

        # Store area name for display
        order["restaurant_area"] = area_name

        # Store subtotal
        order["subtotal"] = subtotal

        # Get order description if available
        order_description = order.get("order_description", "No special instructions")
        # Escape Markdown in order description
        safe_order_description = escape_markdown(order_description)

        # Telegram username is stored in order but not used in summary

        # Escape Markdown in delivery name and phone number
        safe_delivery_name = escape_markdown(order["delivery_name"])
        safe_phone_number = escape_markdown(order["phone_number"])

        summary = (
            f"🧾 ORDER SUMMARY - #{order_number}\n\n"
            f"👤 Name: {safe_delivery_name}\n"
            f"📱 Phone: {safe_phone_number}\n"
            f"📍 Delivery to: {safe_delivery_gate}\n\n"
            f"🏪 Restaurant: {safe_restaurant_name}\n\n"
            f"📋 Items:\n{order_details}\n"
            f"📝 Special Instructions: {safe_order_description}\n\n"
            f"💰 Subtotal: {subtotal} birr\n"
            f"🚚 Delivery Fee: {delivery_fee} birr\n"
            f"💵 Total: {total_price} birr\n\n"
            "Please confirm your order to proceed."
        )

        # Create confirmation buttons
        markup = get_order_confirmation_markup()

        # Send the order summary to the user
        bot.send_message(chat_id, summary, reply_markup=markup, parse_mode="Markdown")

        # Update order status
        order_status[user_id] = "AWAITING_CONFIRMATION"

        return True
    except Exception as e:
        logger.error(f"Error in generate_and_send_order_summary: {e}")
        bot.send_message(
            chat_id, "❌ Error generating order summary. Please try again."
        )
        return False


def register_handlers():
    """Register all handlers in this module"""
    # All handlers are already registered using decorators
    pass
