#!/usr/bin/env python
"""
Test script for the notification bot with actual order data structure.
This script tests the notification functionality with real order data format.
"""

import sys
import os
import telebot
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get notification bot token
NOTIFICATION_BOT_TOKEN = os.getenv(
    "NOTIFICATION_BOT_TOKEN", "8187447788:AAHh_MU3EPnyCz29aYbnPLERUUkPpxyGlrA"
)
NOTIFICATION_CHAT_ID = os.getenv("NOTIFICATION_CHAT_ID", "5546595738")


def test_notification_function(order_data):
    """Test version of the notification function to verify it works with actual order data"""
    try:
        # Extract order details
        order_number = order_data.get("order_number", "Unknown")
        phone_number = order_data.get("phone_number", "Unknown")
        restaurant = order_data.get("restaurant", "Unknown")
        delivery_location = order_data.get("delivery_gate", "Unknown")
        order_items = order_data.get("items", [])
        special_instructions = order_data.get("order_description", "")

        print(f"Processing notification for order {order_number}")
        print(f"Restaurant: {restaurant}")
        print(f"Items count: {len(order_items)}")

        # Format order items
        items_text = ""
        if order_items and isinstance(order_items, list):
            for item in order_items:
                if isinstance(item, dict):
                    item_name = item.get("name", "Unknown item")
                    item_quantity = item.get("quantity", 1)
                    item_price = item.get("price", 0)
                    items_text += (
                        f"• {item_name} (x{item_quantity}) - {item_price} birr\n"
                    )

        # Create the message in the required format
        message_text = f"{order_number}\n\n"
        message_text += f"📱 Phone: {phone_number}\n\n"
        message_text += f"🏪 Restaurant: {restaurant}\n"
        message_text += f"📍 Delivery to: {delivery_location}\n\n\n"
        message_text += f"📋 ORDER ITEMS:\n{items_text}\n"

        # Add special instructions if any
        if special_instructions:
            message_text += f"📝 Special Instructions:\n{special_instructions}\n"

        return message_text
    except Exception as e:
        print(f"Error: {e}")
        return None


def main():
    """Test the notification function with sample order data"""
    print("Testing notification function with sample order data")

    # Create sample order data that matches the actual structure
    sample_order = {
        "order_number": "1930488793_2505051913_0003",
        "phone_number": "0979801635",
        "restaurant": "Helen Megeb-bet",
        "delivery_gate": "Federal Dorm",
        "items": [
            {"name": "Special Firfir", "quantity": 1, "price": 110},
            {"name": "Pasta be Atkilt", "quantity": 1, "price": 100},
        ],
        "order_description": "berbere",
    }

    # Generate the notification message
    notification_message = test_notification_function(sample_order)

    if notification_message:
        print("\nGenerated message:")
        print("-----------------")
        print(notification_message)

        # Now actually send the message if desired
        try:
            if input("\nSend to Telegram? (y/n): ").lower() == "y":
                # Create notification bot instance
                notification_bot = telebot.TeleBot(NOTIFICATION_BOT_TOKEN)

                # Get bot info
                bot_info = notification_bot.get_me()
                print(f"Connected to bot: @{bot_info.username}")

                # Send message
                print(f"Sending to chat ID: {NOTIFICATION_CHAT_ID}")
                notification_bot.send_message(
                    NOTIFICATION_CHAT_ID, notification_message
                )
                print("Message sent successfully!")
        except Exception as e:
            print(f"Error sending message: {e}")

    return 0


if __name__ == "__main__":
    sys.exit(main())
