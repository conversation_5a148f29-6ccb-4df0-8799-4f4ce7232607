# Firebase Integration Setup Guide

This guide explains how to migrate from JSON file storage to Firebase Realtime Database for the Telegram Delivery Bot.

## Step 1: Create a Firebase Project

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Click on "Add project" and follow the setup wizard
3. Give your project a name (e.g., "tg-delivery-bot")
4. Enable or disable Google Analytics as per your preference
5. Click "Create project"

## Step 2: Set up Realtime Database

1. In the Firebase Console, select your project
2. In the left sidebar, click on "Build" > "Realtime Database"
3. Click "Create Database"
4. Choose your database location (select the region closest to your users)
5. Start in "Test mode" for development or "Locked mode" for production
   - For test mode, set the security rules expiration to a suitable date
6. Click "Enable"

## Step 3: Get Firebase Credentials

1. In the Firebase Console, go to Project Settings (gear icon)
2. Go to the "Service accounts" tab
3. Click "Generate new private key" for the Firebase Admin SDK
4. Save the JSON file as `firebase-credentials.json` in your project's root directory
   - **IMPORTANT**: Never commit this file to a public repository

## Step 4: Update Environment Variables

Create or modify your `.env` file to include these Firebase configurations:

```
# Enable Firebase
USE_FIREBASE=true

# Your Firebase database URL
FIREBASE_DATABASE_URL=https://your-project-id.firebaseio.com

# Optional: You can also set the Firebase credentials directly in the .env file
# This is useful for deployment environments like Railway
# FIREBASE_CREDENTIALS={"type":"service_account","project_id":"your-project-id",...}
```

## Step 5: Migrate Existing Data (Optional)

If you already have data in JSON files that you want to migrate to Firebase:

1. Ensure your Firebase credentials and database URL are set up correctly
2. Run the migration script:

```bash
python src/migrate_to_firebase.py
```

This script will:
- Read all existing JSON data files
- Upload the data to the corresponding locations in your Firebase Realtime Database
- Log the migration process to both the console and a `migration.log` file

## Step 6: Deploy to Railway

1. Ensure your GitHub repository is connected to Railway
2. In Railway, add the required environment variables:
   - `TELEGRAM_BOT_TOKEN`
   - `ADMIN_CHAT_IDS`
   - `FINANCE_CHAT_IDS`
   - `USE_FIREBASE=true`
   - `FIREBASE_DATABASE_URL`
   - `FIREBASE_CREDENTIALS` (paste the entire content of your credentials JSON file)
3. Deploy your application

## Firebase Database Structure

The Firebase Realtime Database will have the following structure:

```
/
├── user_points/                 # User points balances
│   ├── {user_id}: points
├── user_names/                  # User names
│   ├── {user_id}: name
├── user_phone_numbers/          # User phone numbers
│   ├── {user_id}: phone
├── user_emails/                 # User email addresses
│   ├── {user_id}: email
├── user_order_history/          # User order history
│   ├── {user_id}: [orders]
├── favorite_orders/             # User favorite orders
│   ├── {user_id}: [orders]
├── current_orders/              # Active orders being created
│   ├── {user_id}: order_data
├── order_status/                # Status of orders in progress
│   ├── {user_id}: status
├── pending_admin_reviews/       # Orders waiting for admin review
│   ├── {order_number}: order_data
├── admin_remarks/               # Admin remarks on orders
│   ├── {order_number}: remarks
├── awaiting_receipt/            # Orders waiting for payment receipt
│   ├── {order_number}: order_data
├── areas/                       # Areas configuration
├── restaurants/                 # Restaurants configuration
├── menus/                       # Menus configuration
├── delivery_locations/          # Delivery locations
└── delivery_fees/               # Delivery fees
```

## Troubleshooting

- **Firebase Initialization Error**: Check that your credentials and database URL are correct
- **Permission Denied**: Verify your security rules in the Firebase Console
- **Migration Issues**: Check the migration.log file for specific errors
- **Data Not Updating**: Ensure USE_FIREBASE is set to "true" in your .env file

## Security Considerations

- Never commit your Firebase credentials to a public repository
- In production, set appropriate security rules for your Firebase database
- Consider enabling Firebase Authentication for secure access
- Regularly backup your Firebase data 