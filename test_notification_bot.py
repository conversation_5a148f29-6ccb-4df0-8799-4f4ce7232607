#!/usr/bin/env python
"""
Test script for the notification bot.
This script sends a test message to the notification channel.
"""

import sys
import os
import telebot
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get notification bot token
NOTIFICATION_BOT_TOKEN = os.getenv(
    "NOTIFICATION_BOT_TOKEN", "8187447788:AAHh_MU3EPnyCz29aYbnPLERUUkPpxyGlrA"
)
NOTIFICATION_CHAT_ID = os.getenv("NOTIFICATION_CHAT_ID", "5546595738")


def main():
    """Send a test message to the notification channel"""
    print(
        "Initializing notification bot with token:", NOTIFICATION_BOT_TOKEN[:10] + "..."
    )
    print("Target chat ID:", NOTIFICATION_CHAT_ID)

    # Create notification bot instance
    notification_bot = telebot.TeleBot(NOTIFICATION_BOT_TOKEN)

    # Create sample order data
    sample_order = """1930488793_2505051913_0003

📱 Phone: 0979801635

🏪 Restaurant: <PERSON>-bet
📍 Delivery to: Federal Dorm


📋 ORDER ITEMS:
• Special Firfir (x1) - 110 birr
• Pasta be Atkilt (x1) - 100 birr

📝 Special Instructions:
berbere"""

    try:
        # Get bot info
        bot_info = notification_bot.get_me()
        print(f"Connected to bot: @{bot_info.username}")

        # Send test message
        print("Sending test message...")
        notification_bot.send_message(NOTIFICATION_CHAT_ID, sample_order)
        print("Test message sent successfully!")

    except Exception as e:
        print(f"Error: {e}")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
