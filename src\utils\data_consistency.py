"""
Data consistency utilities for the Wiz Aroma Delivery Bot.
"""

import os
import json
import time
import threading
import functools
from typing import Dict, List, Any, Optional, Callable
import logging

from src.utils.logging_utils import get_logger

# Get the logger
logger = get_logger()


class DataConsistencyManager:
    """
    Manages data consistency and automatic refresh
    """

    def __init__(self):
        self.data_types = {}
        self.refresh_thread = None
        self.running = False
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        self.last_heartbeat = time.time()

    def heartbeat(self):
        """Update the heartbeat timestamp to indicate the manager is alive"""
        self.last_heartbeat = time.time()
        return self.last_heartbeat

    def get_last_heartbeat(self):
        """Get the timestamp of the last heartbeat"""
        return self.last_heartbeat

    def is_alive(self):
        """Check if the manager is alive based on heartbeat"""
        return time.time() - self.last_heartbeat < 60  # 60 seconds timeout

    def register_data_type(self, data_type, refresh_interval, refresh_callback):
        """
        Register a data type for consistency management
        """
        with self.lock:
            self.data_types[data_type] = {
                "refresh_interval": refresh_interval,
                "refresh_callback": refresh_callback,
                "last_refresh": 0,
            }
            self.logger.debug(
                f"Registered data type {data_type} for refresh every {refresh_interval} seconds"
            )

    def start_refresh_thread(self):
        """
        Start the refresh thread
        """
        if self.refresh_thread is not None and self.refresh_thread.is_alive():
            self.logger.warning("Refresh thread is already running")
            return

        self.running = True
        self.refresh_thread = threading.Thread(target=self._refresh_loop, daemon=True)
        self.refresh_thread.start()
        self.logger.info("Started data consistency refresh thread")

    def stop_refresh_thread(self):
        """
        Stop the refresh thread
        """
        self.running = False
        if self.refresh_thread is not None:
            try:
                self.refresh_thread.join(timeout=5)
                self.logger.info("Stopped data refresh thread")
            except Exception as e:
                self.logger.error(f"Error stopping refresh thread: {e}")
                # The thread is daemon, so it will be terminated when the main thread exits
                self.logger.info("Refresh thread will be terminated on exit")

    def _refresh_loop(self):
        """
        The main refresh loop that runs in a separate thread
        """
        try:
            while self.running:
                current_time = time.time()

                # Update heartbeat
                self.heartbeat()

                # Process each data type
                for data_type, info in list(self.data_types.items()):
                    try:
                        # Check if it's time to refresh this data type
                        if (
                            current_time - info["last_refresh"]
                            >= info["refresh_interval"]
                        ):
                            try:
                                # Call the refresh callback
                                info["refresh_callback"]()
                                # Update the last refresh time
                                with self.lock:
                                    if (
                                        data_type in self.data_types
                                    ):  # Check again to avoid race conditions
                                        self.data_types[data_type][
                                            "last_refresh"
                                        ] = current_time
                                self.logger.debug(f"Refreshed data type: {data_type}")
                            except Exception as e:
                                self.logger.error(
                                    f"Error refreshing data type {data_type}: {e}"
                                )
                                # Continue with other data types even if this one failed
                    except Exception as e:
                        self.logger.error(
                            f"Error processing data type {data_type}: {e}"
                        )
                        # Continue with other data types

                # Sleep for a short time to avoid CPU overuse
                for _ in range(10):  # Check running flag every 0.1 seconds
                    if not self.running:
                        break
                    time.sleep(0.1)
        except Exception as e:
            self.logger.error(f"Error in refresh loop: {e}")
            # Try to restart the thread if it fails
            if self.running:
                self.logger.info("Attempting to restart refresh thread...")
                try:
                    self.refresh_thread = threading.Thread(
                        target=self._refresh_loop, daemon=True
                    )
                    self.refresh_thread.start()
                    self.logger.info("Refresh thread restarted")
                except Exception as restart_error:
                    self.logger.error(
                        f"Failed to restart refresh thread: {restart_error}"
                    )
                    # Set running to False to prevent further restart attempts
                    self.running = False

    def with_data_lock(self, data_type: str):
        """
        Decorator to ensure data consistency with locks.

        Args:
            data_type: Type of data to lock

        Returns:
            Decorated function
        """

        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                if data_type not in self.data_types:
                    self.register_data_type(data_type)

                with self.lock:
                    result = func(*args, **kwargs)
                    self.data_types[data_type]["last_refresh"] = time.time()
                    return result

            return wrapper

        return decorator

    def validate_json_file(self, file_path: str, default_value: Any = None) -> bool:
        """
        Validate a JSON file and repair if necessary.

        Args:
            file_path: Path to the JSON file
            default_value: Default value to use if file is corrupted

        Returns:
            True if file is valid or was repaired, False otherwise
        """
        if not os.path.exists(file_path):
            logger.warning(f"File not found: {file_path}")
            return False

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                json.load(f)
            return True
        except json.JSONDecodeError:
            logger.error(f"Corrupted JSON file: {file_path}")

            # Try to repair by using backup
            backup_path = f"{file_path}.backup"
            if os.path.exists(backup_path):
                try:
                    with open(backup_path, "r", encoding="utf-8") as f:
                        data = json.load(f)

                    with open(file_path, "w", encoding="utf-8") as f:
                        json.dump(data, f, indent=4, ensure_ascii=False)

                    logger.info(f"Repaired {file_path} from backup")
                    return True
                except Exception as e:
                    logger.error(f"Failed to repair {file_path} from backup: {str(e)}")

            # If no backup or backup is also corrupted, use default value
            if default_value is not None:
                try:
                    with open(file_path, "w", encoding="utf-8") as f:
                        json.dump(default_value, f, indent=4, ensure_ascii=False)

                    logger.info(f"Repaired {file_path} with default value")
                    return True
                except Exception as e:
                    logger.error(
                        f"Failed to repair {file_path} with default value: {str(e)}"
                    )

            return False


# Create a singleton instance
data_consistency_manager = DataConsistencyManager()


def get_data_consistency_manager():
    """Get the singleton data consistency manager instance."""
    return data_consistency_manager


def with_data_lock(data_type: str):
    """
    Decorator to ensure data consistency with locks.

    Args:
        data_type: Type of data to lock

    Returns:
        Decorated function
    """
    return data_consistency_manager.with_data_lock(data_type)
